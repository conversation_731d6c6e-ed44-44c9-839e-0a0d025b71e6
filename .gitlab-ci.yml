variables:
  VERACODE_VERSION: *********
  VERACODE_ID: e94cf23b72a6bb24411de292ce9541f7
  VERACODE_KEY: 4f821ebc93a1acf4b9e103216845a8fe9e282ab9d56791bc112776d1442fb194966f277a73b2772cfc6126b520e8fbbe55cc520cd9dafcec3ac53be98c42eea6
  VERACODE_APP_NAME: KFD-KFAS-KFHUB-TATM-LIB
  VERACODE_APP_ARCHIVE_NAME: kfhub_tatm_lib
default:
  tags:
    - kfhub-gitlab-build
  before_script:
    - export TERM=xterm
    - wget -q -O veracode-wrapper.jar https://repo1.maven.org/maven2/com/veracode/vosp/api/wrappers/vosp-api-wrappers-java/${VERACODE_VERSION}/vosp-api-wrappers-java-${VERACODE_VERSION}.jar
    - touch ${VERACODE_APP_ARCHIVE_NAME}.tar.gz
    - tar --exclude=veracode-wrapper.jar --exclude=node_modules --exclude=__tests__ --exclude=dist --exclude=release --exclude=documentation --exclude=temp --exclude=coverage --exclude=.angular --exclude=last-build --exclude=.git --exclude=.idea --exclude=.DS_Store --exclude=.ssh --exclude=${VERACODE_APP_ARCHIVE_NAME}.tar.gz -zcf ${VERACODE_APP_ARCHIVE_NAME}.tar.gz .
  retry:
    max: 2
    when:
      - data_integrity_failure
      - scheduler_failure
      - stale_schedule
      - stuck_or_timeout_failure
      - script_failure
      - unknown_failure
stages:
  - scan_dev
  - scan_release
scan_dev:
  stage: scan_dev
  image: openjdk:10
  rules:
    - if: $CI_COMMIT_TAG =~ /-manual--deployment--\d{14}$/
      when: manual
      allow_failure: true
    - if: $CI_COMMIT_TAG =~ /-(pretest|test)--deployment--\d{14}$/
      when: on_success
      allow_failure: true
    - if: $CI_COMMIT_REF_NAME =~ /^develop-qa$/ && $CI_COMMIT_MESSAGE !~ /\sversion\sto/
      when: on_success
      allow_failure: true
  script:
    - java -jar veracode-wrapper.jar -sandboxname dev-sandbox -createsandbox true -action UploadAndScan -createprofile true -autoscan true -appname "${VERACODE_APP_NAME}" -version "ref-${CI_COMMIT_REF_NAME}-$(date -u +%y%m%d-%H%M%S)" -filepath ${VERACODE_APP_ARCHIVE_NAME}.tar.gz -vid ${VERACODE_ID} -vkey ${VERACODE_KEY} -maxretrycount 10 -debug
scan_release:
  stage: scan_release
  image: openjdk:10
  rules:
    - if: $CI_COMMIT_TAG =~ /-manual--deployment--\d{14}$/
      when: manual
      allow_failure: true
    - if: $CI_COMMIT_TAG =~ /-stag(e|ing)--deployment--\d{14}$/
      when: on_success
      allow_failure: true
    - if: $CI_COMMIT_REF_NAME =~ /^(release|hotfix)\/.+/ && $CI_COMMIT_MESSAGE !~ /\sversion\sto/
      when: on_success
      allow_failure: true
  script:
    - java -jar veracode-wrapper.jar -sandboxname release-sandbox -createsandbox true -action UploadAndScan -createprofile true -autoscan true -appname "${VERACODE_APP_NAME}" -version "ref-${CI_COMMIT_REF_NAME}-$(date -u +%y%m%d-%H%M%S)" -filepath ${VERACODE_APP_ARCHIVE_NAME}.tar.gz -vid ${VERACODE_ID} -vkey ${VERACODE_KEY} -maxretrycount 10 -debug
