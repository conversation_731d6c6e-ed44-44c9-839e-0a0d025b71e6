{"name": "@kf-products-core/kfhub_tatm_lib", "version": "5.8.0-867", "build": {"date": "2025-09-08 14:49:00 GMT", "author": "Annu-Ag <<EMAIL>>", "source": {"ref": {"type": "branch", "value": "release/september-2025"}}}, "dependencies": {"@angular-devkit/build-angular": "16.1.4", "@angular-devkit/core": "16.1.4", "@angular-devkit/schematics": "16.1.4", "@angular-eslint/builder": "^16.1.0", "@angular-eslint/eslint-plugin": "~16.0.3", "@angular-eslint/eslint-plugin-template": "~16.0.3", "@angular-eslint/schematics": "^16.1.0", "@angular-eslint/template-parser": "~16.0.3", "@angular/animations": "~16.1.4", "@angular/cdk": "~16.1.4", "@angular/cli": "16.1.4", "@angular/common": "~16.1.4", "@angular/compiler": "~16.1.4", "@angular/compiler-cli": "16.1.4", "@angular/core": "~16.1.4", "@angular/forms": "~16.1.4", "@angular/language-service": "16.1.4", "@angular/platform-browser": "~16.1.4", "@angular/platform-browser-dynamic": "~16.1.4", "@angular/router": "~16.1.4", "@fullcalendar/core": "6.0.3", "@kf-products-core/kfhub_cicd": "5.0.6", "@kf-products-core/kfhub_lib": "7.2.0-961", "@kf-products-core/kfhub_thcl_lib": "7.2.0-2038", "@ng-idle/core": "^12.0.4", "@ngx-translate/core": "~15.0.0", "@ngx-translate/http-loader": "~8.0.0", "@testing-library/angular": "^14.3.0", "@testing-library/jest-dom": "^5.9.0", "@tweenjs/tween.js": "17.2.0", "@types/file-saver": "^2.0.7", "@types/highcharts": "^7.0.0", "@types/jest": "~29.2.5", "@types/jquery": "^3.5.20", "@types/lodash": "4.14.121", "@types/node": "^18.0.0", "@types/object-hash": "1.3.4", "@types/quill": "1.3.3", "@typescript-eslint/eslint-plugin": "~5.48.1", "@typescript-eslint/parser": "~5.48.1", "ajv": "^8.8.2", "bootstrap": "~5.1.3", "chart.js": "^3.3.2", "codelyzer": "^6.0.0", "concurrently": "3.5.1", "core-js": "^3.30.2", "cross-env": "^7.0.2", "d3": "4.13.0", "dayjs": "1.11.7", "del": "^6.0.0", "eslint": "~8.32.0", "eslint-config-airbnb-typescript": "~17.0.0", "eslint-config-prettier": "~8.10.0", "eslint-plugin-import": "~2.27.4", "eslint-plugin-jsdoc": "~39.6.4", "eslint-plugin-prefer-arrow": "~1.2.3", "eslint-plugin-prettier": "~4.2.1", "file-saver": "^2.0.5", "font-awesome": "4.7.0", "http-server": "^0.12.3", "husky": "~8.0.3", "immer": "7.0.5", "jest": "~29.5.0", "jest-createspyobj": "^2.0.0", "jest-environment-jsdom": "^29.7.0", "jest-preset-angular": "^13.1.6", "jquery": "^3.6.0", "jsoneditor": "^9.3.0", "jsontool": "7.0.2", "lint-staged": "~13.1.0", "lodash": "4.17.21", "ng-packagr": "~16.1.0", "ng2-dragula": "~5.0.0", "ngx-cookie-service": "^16.0.0", "ngx-device-detector": "^6.0.0", "ngx-file-drop": "^15.0.0", "ngx-quill": "^22.0.0", "nodemailer-cli": "2.1.1", "object-hash": "^3.0.0", "prettier": "^2.8.3", "primeng": "16.0.0-rc.2", "prismjs": "~1.29.0", "protractor": "~7.0.0", "quill": "^1.3.7", "ramda": "~0.28.0", "rxjs": "~7.8.0", "semver-compare": "1.0.0", "signalr": "^2.4.2", "ts-jest": "^29.1.0", "ts-node": "~10.9.1", "tslib": "~2.4.1", "typescript": "~5.0.4", "util": "^0.12.5", "uuid": "^9.0.0", "zone.js": "~0.13.1"}}