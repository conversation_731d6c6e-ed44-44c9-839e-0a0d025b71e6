import { Injectable } from '@angular/core';
import { KfAuthService, KfLoadingControllerService } from '@kf-products-core/kfhub_lib';
import { empty, from, Observable, of, Subject, throwError } from 'rxjs';
import { catchError, finalize, mergeMap, mergeScan, tap, toArray } from 'rxjs/operators';
import { KfTatmConstantsService } from './kftatm-constants.service';

import JSZip from 'jszip';

declare global {
    interface Navigator {
        msSaveBlob?: (blob: any, defaultName?: string) => boolean
    }
}

const simultaneousDownloads = 3; // '1' will make 'mergeScan' process items one by one
const zipThreshold = 10; // if not so many reports - download them one by one

/**
 * Stateless service to manage downloads
 */
@Injectable()
export class KfTatmDownloaderService {
    // multiple requests can be executed at same time
    // spinner will be closed when all requests are done
    private spinnerCount = 0;
    constructor(
        private auth: KfAuthService,
        private constantsService: KfTatmConstantsService,
        public spinner: KfLoadingControllerService,
    ) {}

    /**
     * Download requested files one by one and save them
     * if amount of files is bigger tham zipThreshold - add all files into single zip archive
     */
    public downloadFiles(urls: string[], zipName: string = 'reports.zip'): Observable<any> {
        this.spinnerOpen();

        const results: any[] = [];

        const fileSource = from(urls).pipe(
            mergeScan(
                (acc, url) =>
                    this.getFile(url).pipe(
                        catchError((err) => {
                            console.error(err);
                            results.push({ error: true, url, err });
                            return empty();
                        }),
                        tap((response) => {
                            results.push({ error: false, response });
                        }),
                    ),
                null,
                simultaneousDownloads,
            ),
        );

        return fileSource.pipe(
            toArray(),
            mergeMap(() => {
                const failedRequests = results.filter((result) => result.error);
                const successfulRequests = results.filter((result) => !result.error);

                if (failedRequests.length > 0) {
                    this.spinnerClose();

                    const errorMessage =
                        failedRequests.length === urls.length
                            ? 'ALL_DOWNLOAD_FAILED'
                            : 'PARTIAL_DOWNLOAD_FAILED';

                    const error = new Error(errorMessage);
                    error['failedRequests'] = failedRequests;

                    if (successfulRequests.length > 0) {
                        return this.processSuccessfulRequests(
                            successfulRequests,
                            zipName,
                            urls.length,
                        ).pipe(mergeMap(() => throwError(error)));
                    }

                    return throwError(error);
                }

                return this.processSuccessfulRequests(
                    successfulRequests,
                    zipName,
                    urls.length,
                ).pipe(tap(() => this.spinnerClose()));
            }),
            tap({
                next: () => this.spinnerClose(),
                error: () => this.spinnerClose(),
            }),
        );
    }

    private processSuccessfulRequests(
        successfulRequests: any[],
        zipName: string,
        urlCount: number,
    ): Observable<any> {
        if (urlCount <= zipThreshold) {
            successfulRequests.forEach((file) =>
                this.saveFile(file.response.response, file.response.fileName),
            );
            return of(successfulRequests);
        } else {
            const zip = new JSZip();
            const filesCount = {};

            successfulRequests.forEach(({ response }) => {
                let { fileName, response: fileResponse } = response;
                if (filesCount[fileName] >= 0) {
                    fileName = fileName.replace(
                        /(\.[\w\d_-]+)$/i,
                        ` (${++filesCount[fileName]})$1`,
                    );
                } else {
                    filesCount[fileName] = 0;
                }
                zip.file(fileName, fileResponse);
            });

            return from(zip.generateAsync({ type: 'blob' })).pipe(
                tap((content) => this.saveFile(content, zipName)),
            );
        }
    }

    /**
     * Download and save single file
     */
    public downloadFile(url: string, fileName?: string, params: any = null): Observable<any> {
        this.spinnerOpen();
        return this.getFile(url, params).pipe(
            tap((file) => this.saveFile(file.response, fileName || file.fileName)),
            catchError((error) => {
                error['failedRequests'] = [{ error: true, url, err: error }];
                return throwError(error);
            }),
            tap(() => this.spinnerClose()),
            finalize(() => this.spinnerClose()),
        );
    }

    getFile(url: string, params: any = null): Observable<any> {
        const subject = new Subject();
        const fullUrl = url;
        const xhr = new XMLHttpRequest();

        xhr.open(params == null ? 'GET' : 'POST', fullUrl, true);
        xhr.setRequestHeader('Content-Type', 'application/json');
        xhr.setRequestHeader('authToken', this.auth.AuthToken);

        xhr.onreadystatechange = () => {
            if (xhr.readyState === XMLHttpRequest.DONE) {
                if (xhr.status >= 200 && xhr.status < 300) {
                    const response = xhr.response;
                    const contentDispositionHeader = xhr.getResponseHeader('Content-Disposition');
                    const fileName = this.getFileNameFromHttpResponse(contentDispositionHeader);

                    subject.next({ response, fileName });
                    subject.complete();
                } else {
                    subject.error({ url, status: xhr.status });
                    subject.complete();
                }
            }
        };
        xhr.onerror = xhr.onabort = (ev) => {
            subject.error(ev);
            subject.complete();
        };
        xhr.responseType = 'blob';
        if (params) {
            xhr.send(params);
        } else {
            xhr.send();
        }

        return subject;
    }

    saveFile(blob: any, fileName: string) {
        // TODO: IE detection
        let isIE = false;
        const match = navigator.userAgent.search(/(?:Edge|MSIE|Trident\/.*; rv:)/);
        let isMozilla = false;
        if (navigator.userAgent.toLowerCase().indexOf('firefox') > -1) {
            isMozilla = true;
        } else if (match !== -1) {
            isIE = true;
        }
        try {
            if (isIE) {
                if (navigator.msSaveBlob) {
                    navigator.msSaveBlob(blob, fileName);
                }
            } else {
                if (isMozilla) {
                    const file = new File([blob], fileName, { type: 'application/force-download' });
                    window.open(URL.createObjectURL(file));
                } else {
                    const link = document.createElement('a');
                    link.href = window.URL.createObjectURL(blob);
                    link.download = fileName;
                    link.click();
                }
            }
        } catch (e) {}
    }

    getFileNameFromHttpResponse(contentDispositionHeaderResponse) {
        const encodedFileName = contentDispositionHeaderResponse.split(';')[1].trim().split('=')[1];
        const fileName = decodeURIComponent(encodedFileName.replace(/"/g, ''));
        return fileName;
    }

    spinnerOpen() {
        this.spinnerCount += 1;
        this.spinner.spinnerOpen();
    }

    spinnerClose() {
        this.spinnerCount -= 1;
        if (this.spinnerCount < 1) {
            this.spinnerCount = 0;
            this.spinner.spinnerClose();
        }
    }
}
